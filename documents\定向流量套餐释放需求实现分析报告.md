# 定向流量套餐释放需求实现分析报告

## 1. 概述

### 1.1 业务背景
定向流量套餐释放是移动通信网络中的核心业务功能，主要负责处理已激活套餐的过期释放、资源回收和状态更新。PackageOverdueService.handleActivatedOne方法是该业务的核心实现，承担着套餐生命周期管理的关键职责。

### 1.2 方法职责
- **套餐过期处理**: 处理已激活状态套餐的过期释放
- **资源回收**: 回收套餐相关的网络资源和数据库记录
- **状态同步**: 与UPCC、HSS等网元系统进行状态同步
- **并发控制**: 通过分布式锁确保套餐处理的原子性

## 2. handleActivatedOne方法深度实现分析

### 2.1 方法签名和核心结构

```java
@Transactional(rollbackFor = Exception.class)
public void handleActivatedOne(ChannelPackageCard packageCard) {
    try {
        // 1. 分布式锁获取
        boolean lock = redissonLock.tryLock(packageCard.getPackageUniqueId());
        if (!lock) {
            log.warn("当前套餐正在其它流程处理，此次不进行过期处理");
            return;
        }
        
        // 2. 达量释放状态校验
        if (ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue().equals(packageCard.getControlLogic())) {
            packageCard = packageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .eq(ChannelPackageCard::getPackageUniqueId, packageCard.getPackageUniqueId()));
            if (packageCard.getPackageStatus().equals(ChannelPackageCard.PackageStatusEnum.USED.getValue())) {
                log.warn("此套餐已处理，流程结束");
                return;
            }
            packageCard.setUpdateOverdueTime(false);
        }

        // 3. 流量池套餐验证
        try {
            if (flowpoolPackageVerify(packageCard)) {
                log.info("[已激活套餐过期]套餐未处于流量池最后周期, 不进行处理");
                return;
            }
        } catch (BizException e) {
            log.error("数据异常，回收失败。", e);
            return;
        }
        
        // 4. 渠道类型判断和分发处理
        String corpId = packageCard.getCorpId();
        Channel channel = channelMapper.selectById(corpId);
        String channelType = channel.getType();
        if (Channel.ChannelTypeEnum.TEMMINAL_ONLINE.getValue().equals(channelType)) {
            onlinePackageOverDue(packageCard);
        } else if (Channel.ChannelTypeEnum.TEMMINAL_OFFLINE.getValue().equals(channelType)) {
            offLinePackageOverdue(packageCard);
        } else {
            normalPackageOverDue(packageCard);
        }
    } finally {
        // 5. 分布式锁释放
        if (redissonLock.isHeldByCurrentThread(packageCard.getPackageUniqueId())) {
            redissonLock.unlock(packageCard.getPackageUniqueId());
        }
    }
}
```

### 2.2 ChannelPackageCard参数深度分析

#### 2.2.1 核心业务字段
```java
public class ChannelPackageCard extends BaseEntity {
    private Long id;                    // 主键ID
    private String imsi;                // 用户IMSI
    private String iccid;               // 卡号
    private String packageUniqueId;     // 套餐唯一标识（分布式锁键）
    
    // 套餐基本信息
    private String packageType;        // 套餐类型：1-套餐，2-终端线下卡池，3-流量池，4-加油包
    private String packageId;          // 套餐ID
    private String packageStatus;      // 套餐状态：1-待激活，2-已激活，3-已使用，5-已过期，6-激活中
    
    // 时间控制
    private Date activeTime;           // 激活时间
    private Date expireTime;           // 过期时间
    private Date effectiveDay;         // 有效期
    
    // 业务控制
    private String controlLogic;       // 达量控制逻辑：1-达量限速，2-达量释放
    private String corpId;             // 渠道商ID
    private String cooperationMode;    // 合作模式
    
    // 状态标识
    private boolean updateOverdueTime; // 是否更新过期时间
}
```

#### 2.2.2 关键枚举定义
```java
// 套餐状态枚举
public enum PackageStatusEnum {
    TO_BE_ACTIVATED("1"),    // 待激活
    ACTIVATED("2"),          // 已激活
    USED("3"),              // 已使用
    EXPIRED("5"),           // 已过期
    ACTIVE("6");            // 激活中
}

// 达量控制逻辑枚举
public enum ControlLogicEnum {
    SPEED_LIMIT("1"),       // 达量限速
    REACH_RELEASE("2");     // 达量释放
}

// 套餐类型枚举
public enum PackageTypeEnum {
    PACKAGE("1"),                    // 套餐
    TERMINAL_OFF_CARD_POOL("2"),     // 终端线下卡池
    FLOW_POOL("3"),                  // 流量池
    REFUEL("4"),                     // 加油包
    TERMINAL_LINE_CARD_POOL("5");    // 终端线上套餐
}
```

### 2.3 执行流程详细分析

#### 2.3.1 分布式锁控制机制
```java
// 1. 锁获取
boolean lock = redissonLock.tryLock(packageCard.getPackageUniqueId());
if (!lock) {
    log.warn("当前套餐正在其它流程处理，此次不进行过期处理");
    return;
}

// 2. 锁释放（finally块确保）
finally {
    if (redissonLock.isHeldByCurrentThread(packageCard.getPackageUniqueId())) {
        redissonLock.unlock(packageCard.getPackageUniqueId());
    }
}
```

**锁机制特点**：
- **锁键**: 使用packageUniqueId作为锁键，确保同一套餐的并发安全
- **非阻塞**: 使用tryLock()非阻塞获取锁，避免线程阻塞
- **自动释放**: finally块确保锁的正确释放，防止死锁

#### 2.3.2 达量释放状态校验
```java
if (ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue().equals(packageCard.getControlLogic())) {
    // 重新查询最新状态
    packageCard = packageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
            .eq(ChannelPackageCard::getPackageUniqueId, packageCard.getPackageUniqueId()));
    
    // 检查是否已处理
    if (packageCard.getPackageStatus().equals(ChannelPackageCard.PackageStatusEnum.USED.getValue())) {
        log.warn("此套餐已处理，流程结束");
        return;
    }
    packageCard.setUpdateOverdueTime(false);
}
```

**校验逻辑**：
- **双重检查**: 获取锁后重新查询套餐状态，确保数据一致性
- **状态验证**: 检查套餐是否已被其他流程处理
- **标识设置**: 设置updateOverdueTime为false，控制时间更新逻辑

#### 2.3.3 流量池套餐验证
```java
private boolean flowpoolPackageVerify(ChannelPackageCard packageCard) {
    if (ChannelPackageCard.PackageTypeEnum.FLOW_POOL.getValue().equals(packageCard.getPackageType())) {
        // 1. 查询卡信息
        String iccid = packageCard.getIccid();
        ChannelCard channelCard = channelCardMapper.selectOne(Wrappers.lambdaQuery(ChannelCard.class)
                .eq(ChannelCard::getIccid, iccid));
        
        // 2. 检查流量池状态
        if (channelCard.getFlowPoolStatus().equals("2")) {
            return false; // 暂停状态，需要处理
        }
        
        // 3. 查询流量池周期信息
        CmsFlowpoolInfoCycle cmsFlowpoolInfoCycle = 
            flowpoolInfoCycleMapper.selectOne(Wrappers.lambdaQuery(CmsFlowpoolInfoCycle.class)
                .eq(CmsFlowpoolInfoCycle::getFlowPoolUniqueId, packageCard.getBelongPackageId()));
        
        CmsFlowpoolInfo cmsFlowpoolInfo = flowpoolInfoMapper.selectById(cmsFlowpoolInfoCycle.getFlowPoolId());
        
        // 4. 判断是否为最后一个周期
        return cmsFlowpoolInfoCycle.getEndTime().compareTo(cmsFlowpoolInfo.getEndTime()) <= -1;
    }
    return false;
}
```

**验证规则**：
- **流量池类型**: 只对流量池套餐进行特殊验证
- **卡状态检查**: 暂停状态的卡需要进行回收处理
- **周期判断**: 非最后周期的流量池套餐不进行处理

## 3. 定向流量套餐释放机制分析

### 3.1 释放触发条件

#### 3.1.1 时间触发
```java
// 定时任务触发套餐过期处理
@Async
public void handleActivated() {
    // 查询已激活且过期的套餐
    Wrapper<ChannelPackageCard> activedQueryWrapper = Wrappers.<ChannelPackageCard>lambdaQuery()
            .eq(ChannelPackageCard::getPackageStatus, PackageStatusEnum.ACTIVATED.getStatus())
            .ne(ChannelPackageCard::getPackageType, ChannelPackageCard.PackageTypeEnum.REFUEL.getValue())
            .lt(ChannelPackageCard::getExpireTime, new Date());
}
```

#### 3.1.2 达量触发
```java
// 达量释放触发（在HvShareRepository中）
packageOverdueService.handleActivatedOne(channelPackageCard);
```

#### 3.1.3 手动触发
```java
// 手动套餐结束（在PackageEndServiceImpl中）
public Parameter handleActivatedOne(ChannelPackageCard packageCard) {
    // 手动设置过期时间
    if (PackageStatusEnum.ACTIVATED.getStatus().equals(packageStatus)) {
        packageCard.setExpireTime(now);
    } else {
        packageCard.setEffectiveDay(now);
    }
}
```

### 3.2 渠道类型分发处理

#### 3.2.1 标准卡套餐处理 (normalPackageOverDue)
```java
private void normalPackageOverDue(ChannelPackageCard packageCard) {
    String imsi = packageCard.getImsi();
    
    // 1. 验证套餐是否为当前在用套餐
    HcardInfo hcardInfo = Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(imsi));
    if (!packageCard.getPackageUniqueId().equals(hcardInfo.getUpccSignPackageUniqueId())) {
        log.info("当前过期套餐并非此imsi在用套餐，开始套餐回收");
        recyclePackage(packageCard, false, false);
        return;
    }
    
    // 2. 检查延期记录
    CmsPackageDelayRecordInfo packageDelayRecordInfo = cmsPackageDelayRecordInfoMapper.selectOne(
        Wrappers.lambdaQuery(CmsPackageDelayRecordInfo.class)
            .eq(CmsPackageDelayRecordInfo::getHImsi, imsi)
            .eq(CmsPackageDelayRecordInfo::getOldPackageUniqueId, packageCard.getPackageUniqueId()));
    
    if (packageDelayRecordInfo == null) {
        log.debug("没有延期记录，开始套餐回收");
        recyclePackage(packageCard, true, false);
        return;
    }
    
    // 3. 检查延期时间
    Date expireTime = packageCard.getPackageStatus().equals("2") ? 
        packageCard.getExpireTime() : packageCard.getEffectiveDay();
    long between = DateUtil.between(expireTime, new Date(), DateUnit.SECOND);
    if (between > packageDelayTime) {
        log.debug("已超过延期时间，套餐回收流程");
        recyclePackage(packageCard, true, false);
        return;
    }
}
```

#### 3.2.2 终端线上套餐处理 (onlinePackageOverDue)
```java
private void onlinePackageOverDue(ChannelPackageCard packageCard) {
    String imsi = packageCard.getImsi();
    String packageUniqueId = packageCard.getPackageUniqueId();
    
    // 1. 查询其他可用套餐
    Integer otherAvailablePackagesCount = packageCardMapper.selectCount(
        Wrappers.lambdaQuery(ChannelPackageCard.class)
            .eq(ChannelPackageCard::getImsi, imsi)
            .ne(ChannelPackageCard::getPackageUniqueId, packageUniqueId)
            .and(wrapper -> wrapper.eq(ChannelPackageCard::getPackageStatus, "1")
                .or().eq(ChannelPackageCard::getPackageStatus, "6")
                .or().and(w -> w.eq(ChannelPackageCard::getPackageStatus, "2")
                    .gt(ChannelPackageCard::getExpireTime, new Date()))));
    
    // 2. 无其他套餐时进行HSS/UPCC销户
    if (otherAvailablePackagesCount == 0) {
        boolean delHssSubscriberResult = delHssSubscriber(imsi);
        boolean delUpccSubscriberResult = true;
        
        if (Response.getAndCheckRemoteData(pmsFeignClient.getIsSignUpcc(imsi))) {
            delUpccSubscriberResult = delUpccSubscriber(Collections.singleton(imsi), CardTypeEnum.V_CARD);
        } else {
            freezeVimsiBatch(Collections.singleton(imsi));
        }
    }
    
    // 3. 更新套餐状态和上网记录
    Date now = new Date();
    ChannelPackageCard channelPackageCard = new ChannelPackageCard();
    channelPackageCard.setId(packageCard.getId());
    channelPackageCard.setPackageStatus(packageStatus.equals("1") ? 
        ChannelPackageCard.PackageStatusEnum.EXPIRED.getValue() :
        ChannelPackageCard.PackageStatusEnum.USED.getValue());
    packageCardMapper.updateById(channelPackageCard);
    
    if (!packageStatus.equals("1")) {
        updateSurfEndTime(packageUniqueId, now, false);
        deleteRedisKey(packageCard, true);
    }
}
```

#### 3.2.3 终端线下套餐处理 (offLinePackageOverdue)
```java
private void offLinePackageOverdue(ChannelPackageCard packageCard) {
    String vImsi = packageCard.getImsi();
    VcardInfo vCard = pmsFeignClient.getVcardAccountInfo(vImsi).get();
    CardPoolInfo cardPool = pmsFeignClient.getCardPool(vCard.getPoolId()).get();
    
    if ("1".equals(cardPool.getIsExpireReset())) {
        // UPCC重置流程
        String msisdn = vCard.getMsisdn();
        boolean subscribeUpccResult = true;
        
        if (!UpccOpenStatusEnum.SUCCESS.getVal().equals(vCard.getUpccOpenStatus())) {
            subscribeUpccResult = subscribeUpcc(vImsi, msisdn);
        }
        
        if (subscribeUpccResult) {
            signUpcc(vImsi, msisdn, vCard.getBusinessId());
        }
    }
    
    // 更新套餐状态
    ChannelPackageCard channelPackageCard = new ChannelPackageCard();
    channelPackageCard.setId(packageCard.getId());
    channelPackageCard.setPackageStatus(ChannelPackageCard.PackageStatusEnum.USED.getValue());
    packageCardMapper.updateById(channelPackageCard);
}
```

### 3.3 套餐回收核心逻辑 (recyclePackage)

#### 3.3.1 回收方法签名
```java
public void recyclePackage(ChannelPackageCard channelPackageCard, boolean needUpccSubscriber, boolean mockLu) {
    // needUpccSubscriber: H卡是否需要UPCC销户
    // mockLu: 套餐回收后是否需要模拟LU
}
```

#### 3.3.2 回收处理流程
```java
public void recyclePackage(ChannelPackageCard channelPackageCard, boolean needUpccSubscriber, boolean mockLu) {
    log.debug("进入套餐回收流程： id {}, 套餐状态： {}", channelPackageCard.getId(), channelPackageCard.getPackageStatus());
    String packageUniqueId = channelPackageCard.getPackageUniqueId();

    // 1. 查询上网记录
    List<ChannelSurf> channelSurfs = channelSurfMapper.selectList(Wrappers.lambdaQuery(ChannelSurf.class)
            .select(ChannelSurf::getImsi, ChannelSurf::getMadeImsi, ChannelSurf::getHimsi,
                    ChannelSurf::getInternetType, ChannelSurf::getMcc)
            .eq(ChannelSurf::getPackageUniqueId, packageUniqueId));

    // 2. 分类处理H卡和V卡
    Set<String> himsiSet = new HashSet<>();
    Set<String> vimsiSet = new HashSet<>();

    for (ChannelSurf channelSurf : channelSurfs) {
        if ("H".equals(channelSurf.getInternetType())) {
            himsiSet.add(channelSurf.getImsi());
        } else if ("V".equals(channelSurf.getInternetType())) {
            vimsiSet.add(channelSurf.getImsi());
        }
    }

    // 3. UPCC销户处理
    boolean hDelUpccResult = true;
    boolean vDelUpccResult = true;

    if (needUpccSubscriber && !himsiSet.isEmpty()) {
        hDelUpccResult = delUpccSubscriberForPackageOverDue(himsiSet, CardTypeEnum.H_CARD, packageUniqueId);
    }

    if (!vimsiSet.isEmpty()) {
        vDelUpccResult = delUpccSubscriberForPackageOverDue(vimsiSet, CardTypeEnum.V_CARD, packageUniqueId);
    }

    // 4. 更新套餐状态
    Date now = new Date();
    ChannelPackageCard updatePackageCard = new ChannelPackageCard();
    updatePackageCard.setId(channelPackageCard.getId());
    updatePackageCard.setPackageStatus(ChannelPackageCard.PackageStatusEnum.USED.getValue());
    if (channelPackageCard.isUpdateOverdueTime()) {
        updatePackageCard.setExpireTime(now);
    }
    packageCardMapper.updateById(updatePackageCard);

    // 5. 更新上网记录结束时间
    updateSurfEndTime(packageUniqueId, now, false);

    // 6. 删除Redis相关键
    deleteRedisKey(channelPackageCard, false);

    // 7. 模拟LU处理（如果需要）
    if (mockLu && !himsiSet.isEmpty()) {
        for (String himsi : himsiSet) {
            // 发送模拟LU消息
            mockLuService.sendMockLu(himsi);
        }
    }
}
```

### 3.4 资源回收和清理机制

#### 3.4.1 上网记录更新
```java
public void updateSurfEndTime(String packageUniqueId, Date endTime, Boolean activingPackage) {
    if (activingPackage) {
        // 删除激活中套餐的上网记录
        log.debug("删除套餐上网结束时间 packageUniqueId: {}, endTime: {}", packageUniqueId, endTime);
        channelSurfMapper.delete(Wrappers.lambdaUpdate(ChannelSurf.class)
                .eq(ChannelSurf::getPackageUniqueId, packageUniqueId));
    } else {
        // 更新已激活套餐的结束时间
        log.debug("更新套餐上网结束时间 packageUniqueId: {}, endTime: {}", packageUniqueId, endTime);
        channelSurfMapper.update(null, Wrappers.lambdaUpdate(ChannelSurf.class)
                .set(ChannelSurf::getEndTime, endTime)
                .eq(ChannelSurf::getPackageUniqueId, packageUniqueId));
    }
}
```

#### 3.4.2 UPCC销户处理
```java
private boolean delUpccSubscriber(String imsi, CardTypeEnum cardType, boolean freeze, String packageUniqueId) {
    String msisdn;
    boolean isH = cardType == CardTypeEnum.H_CARD;
    Response response;
    String upccSignId = zeroSpeedUpccId;

    if (isH) {
        // H卡处理
        HcardInfo hCard = getCardByImsi(imsi);
        if (hCard == null) {
            log.warn("UPCC销户imsi: {}对应主卡不存在", imsi);
            return true;
        }

        if (!UpccOpenStatusEnum.SUCCESS.getVal().equals(hCard.getUpccOpenStatus())) {
            log.debug("imsi: {} 对应H卡UPCC开户状态不为1，不需要销户", imsi);
            return true;
        }

        msisdn = hCard.getMsisdn();
        response = controlFeignClient.delUpccSubscriber(msisdn, upccSignId);

        if (response.isOk()) {
            // 删除UPCC记录
            cmsCardUpccRecordMapper.delete(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                    .eq(CmsCardUpccRecord::getImsi, imsi));
        }
    } else {
        // V卡处理
        VcardInfo vCard = pmsFeignClient.getVcardAccountInfo(imsi).get();
        if (vCard == null) {
            log.warn("UPCC销户imsi: {}对应V卡不存在", imsi);
            return true;
        }

        if (!UpccOpenStatusEnum.SUCCESS.getVal().equals(vCard.getUpccOpenStatus())) {
            log.debug("imsi: {} 对应V卡UPCC开户状态不为1，不需要销户", imsi);
            if (freeze && VcardInfo.StatusEnum.ASSIGNED.getValue().equals(vCard.getStatus())) {
                // 冻结V卡
                pmsFeignClient.updateVimsiOpenStatus(UpdateOpenStatusReq.builder()
                        .imsi(imsi)
                        .freeze(freeze)
                        .build());
            }
            return true;
        }

        msisdn = vCard.getMsisdn();
        response = controlFeignClient.delUpccSubscriber(msisdn, null);

        if (response.isOk()) {
            cmsCardUpccRecordMapper.delete(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                    .eq(CmsCardUpccRecord::getImsi, imsi));
        }
    }

    return response.isOk();
}
```

#### 3.4.3 Redis缓存清理
```java
private void deleteRedisKey(ChannelPackageCard packageCard, boolean isTerminal) {
    String packageUniqueId = packageCard.getPackageUniqueId();

    // 1. 删除套餐相关缓存
    redisUtil.delete("package_" + packageUniqueId);
    redisUtil.delete("package_quota_" + packageUniqueId);

    // 2. 删除流量池相关缓存
    if (ChannelPackageCard.PackageTypeEnum.FLOW_POOL.getValue().equals(packageCard.getPackageType())) {
        redisUtil.delete("flowpool_" + packageUniqueId);
        redisUtil.delHash("flowpool_quota", packageCard.getImsi());
    }

    // 3. 删除定向应用相关缓存
    redisUtil.delete("app_group_" + packageUniqueId);
    redisUtil.delete("speed_control_" + packageUniqueId);

    // 4. 终端套餐特殊处理
    if (isTerminal) {
        redisUtil.delete("terminal_package_" + packageCard.getImsi());
    }
}
```

## 4. 技术实现细节分析

### 4.1 分布式锁机制

#### 4.1.1 Redisson配置
```java
@Configuration
public class RedissonConfig {

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
            .setAddress("redis://localhost:6379")
            .setConnectionPoolSize(50)
            .setConnectionMinimumIdleSize(10)
            .setIdleConnectionTimeout(10000)
            .setConnectTimeout(10000)
            .setTimeout(3000);
        return Redisson.create(config);
    }
}
```

#### 4.1.2 锁使用策略
```java
// 锁键设计
String lockKey = packageCard.getPackageUniqueId();

// 非阻塞获取锁
boolean lock = redissonLock.tryLock(lockKey);

// 锁状态检查
if (redissonLock.isHeldByCurrentThread(lockKey)) {
    redissonLock.unlock(lockKey);
}
```

**锁机制优势**：
- **细粒度锁**: 使用packageUniqueId作为锁键，避免全局锁竞争
- **非阻塞**: tryLock()避免线程阻塞，提高系统吞吐量
- **自动释放**: finally块确保锁的正确释放
- **重入支持**: Redisson支持可重入锁

### 4.2 事务控制机制

#### 4.2.1 事务注解配置
```java
@Transactional(rollbackFor = Exception.class)
public void handleActivatedOne(ChannelPackageCard packageCard) {
    // 业务逻辑
}
```

#### 4.2.2 事务边界控制
- **方法级事务**: 在handleActivatedOne方法上设置事务边界
- **异常回滚**: 所有Exception都触发事务回滚
- **嵌套事务**: 调用的子方法继承事务上下文

### 4.3 数据一致性保证

#### 4.3.1 双重检查机制
```java
// 获取锁后重新查询最新状态
if (ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue().equals(packageCard.getControlLogic())) {
    packageCard = packageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
            .eq(ChannelPackageCard::getPackageUniqueId, packageCard.getPackageUniqueId()));

    if (packageCard.getPackageStatus().equals(ChannelPackageCard.PackageStatusEnum.USED.getValue())) {
        log.warn("此套餐已处理，流程结束");
        return;
    }
}
```

#### 4.3.2 状态机控制
```
套餐状态流转：
待激活(1) → 激活中(6) → 已激活(2) → 已使用(3)
                                  ↓
                              已过期(5)
```

### 4.4 与外部系统交互

#### 4.4.1 PMS系统交互
```java
// 查询卡信息
HcardInfo hcardInfo = Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(imsi));
VcardInfo vCard = pmsFeignClient.getVcardAccountInfo(vImsi).get();

// 更新卡状态
pmsFeignClient.updateVimsiOpenStatus(UpdateOpenStatusReq.builder()
        .imsi(imsi)
        .freeze(freeze)
        .build());
```

#### 4.4.2 UPCC系统交互
```java
// UPCC销户
Response response = controlFeignClient.delUpccSubscriber(msisdn, upccSignId);

// UPCC签约
Response signResponse = controlFeignClient.signUpcc(vImsi, msisdn, businessId);
```

#### 4.4.3 HSS系统交互
```java
// HSS销户
boolean delHssSubscriberResult = delHssSubscriber(imsi);
```

## 5. 业务规则和边界条件

### 5.1 套餐过期处理规则

#### 5.1.1 时间判断规则
```java
// 已激活套餐：使用expireTime
Date expireTime = packageCard.getPackageStatus().equals("2") ?
    packageCard.getExpireTime() : packageCard.getEffectiveDay();

// 过期判断
.lt(ChannelPackageCard::getExpireTime, new Date())
```

#### 5.1.2 延期处理规则
```java
// 延期时间检查
long between = DateUtil.between(expireTime, new Date(), DateUnit.SECOND);
if (between > packageDelayTime) {
    log.debug("已超过延期时间，套餐回收流程");
    recyclePackage(packageCard, true, false);
    return;
}
```

### 5.2 流量池套餐特殊规则

#### 5.2.1 周期判断
```java
// 判断是否为最后一个周期
return cmsFlowpoolInfoCycle.getEndTime().compareTo(cmsFlowpoolInfo.getEndTime()) <= -1;
```

#### 5.2.2 状态检查
```java
// 暂停状态的卡需要处理
if (channelCard.getFlowPoolStatus().equals("2")) {
    return false;
}
```

### 5.3 异常情况处理

#### 5.3.1 锁获取失败
```java
boolean lock = redissonLock.tryLock(packageCard.getPackageUniqueId());
if (!lock) {
    log.warn("当前套餐正在其它流程处理，此次不进行过期处理");
    return; // 直接返回，不抛异常
}
```

#### 5.3.2 数据异常处理
```java
try {
    if (flowpoolPackageVerify(packageCard)) {
        log.info("[已激活套餐过期]套餐未处于流量池最后周期, 不进行处理");
        return;
    }
} catch (BizException e) {
    log.error("数据异常，回收失败。", e);
    return; // 记录日志后返回，不影响其他套餐处理
}
```

#### 5.3.3 外部服务异常
```java
// 外部服务调用失败的容错处理
try {
    HcardInfo hcardInfo = Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(imsi));
} catch (Exception e) {
    log.warn("查询卡信息失败，imsi: {}, 错误: {}", imsi, e.getMessage());
    // 根据业务规则决定是否继续处理
}
```

## 6. 性能和安全考虑

### 6.1 高并发场景性能分析

#### 6.1.1 并发瓶颈点识别
```java
// 1. 分布式锁竞争
boolean lock = redissonLock.tryLock(packageCard.getPackageUniqueId());
// 瓶颈：相同packageUniqueId的并发请求会产生锁竞争

// 2. 数据库查询热点
packageCard = packageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
        .eq(ChannelPackageCard::getPackageUniqueId, packageCard.getPackageUniqueId()));
// 瓶颈：频繁的数据库查询可能成为性能瓶颈

// 3. 外部服务调用
HcardInfo hcardInfo = Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(imsi));
// 瓶颈：外部服务的响应时间和可用性
```

#### 6.1.2 性能优化策略
```java
// 1. 批量处理优化
public void batchHandleActivatedPackages(List<ChannelPackageCard> packageCards) {
    // 按渠道类型分组批量处理
    Map<String, List<ChannelPackageCard>> groupedByChannel = packageCards.stream()
        .collect(Collectors.groupingBy(ChannelPackageCard::getCorpId));

    groupedByChannel.forEach((corpId, packages) -> {
        Channel channel = channelMapper.selectById(corpId);
        packages.forEach(pkg -> processPackageByChannelType(pkg, channel));
    });
}

// 2. 异步处理优化
@Async("packageOverdueExecutor")
public CompletableFuture<Void> handleActivatedOneAsync(ChannelPackageCard packageCard) {
    try {
        handleActivatedOne(packageCard);
        return CompletableFuture.completedFuture(null);
    } catch (Exception e) {
        log.error("异步处理套餐过期失败", e);
        return CompletableFuture.failedFuture(e);
    }
}

// 3. 缓存优化
@Cacheable(value = "channelInfo", key = "#corpId")
public Channel getChannelInfo(String corpId) {
    return channelMapper.selectById(corpId);
}
```

#### 6.1.3 性能监控指标
```java
// 性能监控埋点
@Around("execution(* com.ebupt.cmi.clientmanagement.service.PackageOverdueService.handleActivatedOne(..))")
public Object monitorPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
    long startTime = System.currentTimeMillis();
    String packageUniqueId = ((ChannelPackageCard) joinPoint.getArgs()[0]).getPackageUniqueId();

    try {
        Object result = joinPoint.proceed();
        long executionTime = System.currentTimeMillis() - startTime;

        // 记录性能指标
        performanceMetrics.recordExecutionTime("package_overdue_handle", executionTime);

        if (executionTime > performanceThreshold) {
            log.warn("套餐过期处理超时: packageUniqueId={}, 耗时={}ms", packageUniqueId, executionTime);
        }

        return result;
    } catch (Exception e) {
        performanceMetrics.incrementErrorCount("package_overdue_handle");
        throw e;
    }
}
```

### 6.2 分布式锁效率分析

#### 6.2.1 锁竞争分析
```java
// 锁竞争统计
public class LockContentionAnalyzer {
    private final AtomicLong lockAttempts = new AtomicLong(0);
    private final AtomicLong lockFailures = new AtomicLong(0);
    private final AtomicLong lockWaitTime = new AtomicLong(0);

    public boolean tryLockWithMetrics(String lockKey) {
        long startTime = System.currentTimeMillis();
        lockAttempts.incrementAndGet();

        boolean acquired = redissonLock.tryLock(lockKey);

        if (!acquired) {
            lockFailures.incrementAndGet();
        }

        long waitTime = System.currentTimeMillis() - startTime;
        lockWaitTime.addAndGet(waitTime);

        // 记录锁竞争指标
        if (waitTime > lockWaitThreshold) {
            log.warn("锁等待时间过长: lockKey={}, waitTime={}ms", lockKey, waitTime);
        }

        return acquired;
    }

    public double getLockFailureRate() {
        long attempts = lockAttempts.get();
        return attempts > 0 ? (double) lockFailures.get() / attempts : 0.0;
    }
}
```

#### 6.2.2 锁优化策略
```java
// 1. 锁粒度优化
// 原来：使用packageUniqueId作为锁键
String lockKey = packageCard.getPackageUniqueId();

// 优化：根据业务场景细化锁粒度
String lockKey = packageCard.getPackageUniqueId() + "_" + operationType;

// 2. 锁超时设置
boolean lock = redissonLock.tryLock(lockKey, 10, TimeUnit.SECONDS);

// 3. 锁降级策略
public boolean handleActivatedOneWithFallback(ChannelPackageCard packageCard) {
    boolean lock = redissonLock.tryLock(packageCard.getPackageUniqueId());
    if (!lock) {
        // 降级处理：记录到延迟队列
        delayedProcessingQueue.offer(packageCard);
        return false;
    }

    try {
        handleActivatedOne(packageCard);
        return true;
    } finally {
        redissonLock.unlock(packageCard.getPackageUniqueId());
    }
}
```

### 6.3 数据一致性保障

#### 6.3.1 事务一致性
```java
// 1. 分布式事务配置
@GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 30000)
@Transactional(rollbackFor = Exception.class)
public void handleActivatedOne(ChannelPackageCard packageCard) {
    // 业务逻辑
}

// 2. 补偿机制
public void compensateFailedPackageOverdue(String packageUniqueId) {
    try {
        // 查询处理状态
        ChannelPackageCard packageCard = packageCardMapper.selectOne(
            Wrappers.lambdaQuery(ChannelPackageCard.class)
                .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));

        if (packageCard != null && !ChannelPackageCard.PackageStatusEnum.USED.getValue()
                .equals(packageCard.getPackageStatus())) {
            // 重新处理
            handleActivatedOne(packageCard);
        }
    } catch (Exception e) {
        log.error("补偿处理失败: packageUniqueId={}", packageUniqueId, e);
    }
}
```

#### 6.3.2 数据校验机制
```java
// 1. 状态校验
private boolean validatePackageStatus(ChannelPackageCard packageCard) {
    // 检查套餐状态是否允许过期处理
    String status = packageCard.getPackageStatus();
    return ChannelPackageCard.PackageStatusEnum.ACTIVATED.getValue().equals(status) ||
           ChannelPackageCard.PackageStatusEnum.ACTIVE.getValue().equals(status);
}

// 2. 时间校验
private boolean validateExpireTime(ChannelPackageCard packageCard) {
    Date expireTime = packageCard.getExpireTime();
    Date now = new Date();

    // 确保确实已过期
    return expireTime != null && expireTime.before(now);
}

// 3. 业务规则校验
private boolean validateBusinessRules(ChannelPackageCard packageCard) {
    // 检查是否有延期记录
    // 检查是否为流量池最后周期
    // 检查渠道商状态等
    return true;
}
```

### 6.4 安全防护措施

#### 6.4.1 输入验证
```java
public void handleActivatedOne(ChannelPackageCard packageCard) {
    // 1. 参数校验
    if (packageCard == null) {
        throw new IllegalArgumentException("套餐信息不能为空");
    }

    if (StringUtils.isBlank(packageCard.getPackageUniqueId())) {
        throw new IllegalArgumentException("套餐唯一标识不能为空");
    }

    // 2. 业务规则校验
    if (!validatePackageStatus(packageCard)) {
        log.warn("套餐状态不允许过期处理: packageUniqueId={}, status={}",
            packageCard.getPackageUniqueId(), packageCard.getPackageStatus());
        return;
    }

    // 3. 权限校验
    if (!hasPermissionToProcess(packageCard)) {
        throw new SecurityException("无权限处理该套餐");
    }
}
```

#### 6.4.2 操作审计
```java
@Component
public class PackageOverdueAuditLogger {

    public void logPackageOverdueOperation(ChannelPackageCard packageCard, String operation, String result) {
        AuditLog auditLog = AuditLog.builder()
            .packageUniqueId(packageCard.getPackageUniqueId())
            .imsi(packageCard.getImsi())
            .operation(operation)
            .result(result)
            .operatorId(getCurrentOperatorId())
            .operateTime(new Date())
            .build();

        auditLogMapper.insert(auditLog);
    }

    @EventListener
    public void handlePackageOverdueEvent(PackageOverdueEvent event) {
        logPackageOverdueOperation(event.getPackageCard(), "PACKAGE_OVERDUE", event.getResult());
    }
}
```

#### 6.4.3 敏感信息保护
```java
// 1. 日志脱敏
public class SensitiveDataMasker {
    public static String maskImsi(String imsi) {
        if (StringUtils.isBlank(imsi) || imsi.length() < 8) {
            return imsi;
        }
        return imsi.substring(0, 4) + "****" + imsi.substring(imsi.length() - 4);
    }

    public static String maskMsisdn(String msisdn) {
        if (StringUtils.isBlank(msisdn) || msisdn.length() < 8) {
            return msisdn;
        }
        return msisdn.substring(0, 3) + "****" + msisdn.substring(msisdn.length() - 4);
    }
}

// 2. 使用示例
log.info("处理套餐过期: packageUniqueId={}, imsi={}",
    packageCard.getPackageUniqueId(),
    SensitiveDataMasker.maskImsi(packageCard.getImsi()));
```

## 7. 测试方案和监控建议

### 7.1 单元测试方案

#### 7.1.1 核心方法测试
```java
@ExtendWith(MockitoExtension.class)
class PackageOverdueServiceTest {

    @Mock
    private RedissonLock redissonLock;
    @Mock
    private ChannelPackageCardMapper packageCardMapper;
    @Mock
    private ChannelMapper channelMapper;

    @InjectMocks
    private PackageOverdueService packageOverdueService;

    @Test
    void testHandleActivatedOne_Success() {
        // Given
        ChannelPackageCard packageCard = buildTestPackageCard();
        when(redissonLock.tryLock(anyString())).thenReturn(true);
        when(redissonLock.isHeldByCurrentThread(anyString())).thenReturn(true);

        Channel channel = buildTestChannel();
        when(channelMapper.selectById(anyString())).thenReturn(channel);

        // When
        packageOverdueService.handleActivatedOne(packageCard);

        // Then
        verify(redissonLock).tryLock(packageCard.getPackageUniqueId());
        verify(redissonLock).unlock(packageCard.getPackageUniqueId());
    }

    @Test
    void testHandleActivatedOne_LockFailed() {
        // Given
        ChannelPackageCard packageCard = buildTestPackageCard();
        when(redissonLock.tryLock(anyString())).thenReturn(false);

        // When
        packageOverdueService.handleActivatedOne(packageCard);

        // Then
        verify(redissonLock, never()).unlock(anyString());
    }

    @Test
    void testHandleActivatedOne_AlreadyProcessed() {
        // Given
        ChannelPackageCard packageCard = buildTestPackageCard();
        packageCard.setControlLogic(ChannelPackageCard.ControlLogicEnum.REACH_RELEASE.getValue());

        ChannelPackageCard processedCard = buildTestPackageCard();
        processedCard.setPackageStatus(ChannelPackageCard.PackageStatusEnum.USED.getValue());

        when(redissonLock.tryLock(anyString())).thenReturn(true);
        when(packageCardMapper.selectOne(any())).thenReturn(processedCard);

        // When
        packageOverdueService.handleActivatedOne(packageCard);

        // Then
        // 验证流程提前结束
        verify(channelMapper, never()).selectById(anyString());
    }
}
```

#### 7.1.2 边界条件测试
```java
@Test
void testFlowpoolPackageVerify_NotLastCycle() {
    // Given
    ChannelPackageCard flowPoolPackage = buildFlowPoolPackageCard();

    CmsFlowpoolInfoCycle cycle = new CmsFlowpoolInfoCycle();
    cycle.setEndTime(DateUtils.addDays(new Date(), 10));

    CmsFlowpoolInfo flowPoolInfo = new CmsFlowpoolInfo();
    flowPoolInfo.setEndTime(DateUtils.addDays(new Date(), 30));

    when(flowpoolInfoCycleMapper.selectOne(any())).thenReturn(cycle);
    when(flowpoolInfoMapper.selectById(any())).thenReturn(flowPoolInfo);

    // When
    boolean result = packageOverdueService.flowpoolPackageVerify(flowPoolPackage);

    // Then
    assertTrue(result); // 不是最后周期，返回true
}
```

### 7.2 集成测试方案

#### 7.2.1 完整流程测试
```java
@SpringBootTest
@Transactional
class PackageOverdueIntegrationTest {

    @Autowired
    private PackageOverdueService packageOverdueService;

    @Autowired
    private TestDataBuilder testDataBuilder;

    @Test
    void testHandleActivatedOne_NormalPackage_FullFlow() {
        // Given
        ChannelPackageCard packageCard = testDataBuilder.createActivatedPackage();
        testDataBuilder.createChannelData(packageCard.getCorpId());

        // When
        packageOverdueService.handleActivatedOne(packageCard);

        // Then
        ChannelPackageCard updatedCard = packageCardMapper.selectById(packageCard.getId());
        assertEquals(ChannelPackageCard.PackageStatusEnum.USED.getValue(),
            updatedCard.getPackageStatus());
    }

    @Test
    void testHandleActivatedOne_TerminalOnlinePackage() {
        // Given
        ChannelPackageCard packageCard = testDataBuilder.createTerminalOnlinePackage();

        // When
        packageOverdueService.handleActivatedOne(packageCard);

        // Then
        // 验证HSS/UPCC销户是否正确执行
        // 验证上网记录是否正确更新
        // 验证Redis缓存是否正确清理
    }
}
```

### 7.3 性能测试方案

#### 7.3.1 并发测试
```java
@Test
void testConcurrentPackageOverdue() throws InterruptedException {
    int threadCount = 100;
    int packagesPerThread = 10;
    CountDownLatch latch = new CountDownLatch(threadCount);
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);

    for (int i = 0; i < threadCount; i++) {
        executor.submit(() -> {
            try {
                for (int j = 0; j < packagesPerThread; j++) {
                    ChannelPackageCard packageCard = testDataBuilder.createRandomPackage();
                    packageOverdueService.handleActivatedOne(packageCard);
                }
            } finally {
                latch.countDown();
            }
        });
    }

    assertTrue(latch.await(60, TimeUnit.SECONDS));

    // 验证数据一致性
    // 验证性能指标
}
```

### 7.4 监控指标建议

#### 7.4.1 业务监控指标
```java
// 1. 套餐处理量监控
@Component
public class PackageOverdueMetrics {
    private final MeterRegistry meterRegistry;

    public void recordPackageProcessed(String channelType, String result) {
        Counter.builder("package_overdue_processed")
            .tag("channel_type", channelType)
            .tag("result", result)
            .register(meterRegistry)
            .increment();
    }

    public void recordProcessingTime(String operation, long timeMs) {
        Timer.builder("package_overdue_processing_time")
            .tag("operation", operation)
            .register(meterRegistry)
            .record(timeMs, TimeUnit.MILLISECONDS);
    }

    public void recordLockContention(String lockKey, boolean acquired) {
        Counter.builder("package_overdue_lock_contention")
            .tag("lock_key", lockKey)
            .tag("acquired", String.valueOf(acquired))
            .register(meterRegistry)
            .increment();
    }
}
```

#### 7.4.2 告警规则配置
```yaml
# Prometheus告警规则
groups:
  - name: package_overdue_alerts
    rules:
      - alert: PackageOverdueProcessingTimeHigh
        expr: histogram_quantile(0.95, package_overdue_processing_time) > 5000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "套餐过期处理时间过长"
          description: "95%的套餐过期处理时间超过5秒"

      - alert: PackageOverdueLockContentionHigh
        expr: rate(package_overdue_lock_contention{acquired="false"}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "套餐过期处理锁竞争严重"
          description: "锁获取失败率超过10%"

      - alert: PackageOverdueFailureRateHigh
        expr: rate(package_overdue_processed{result="failure"}[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "套餐过期处理失败率过高"
          description: "套餐过期处理失败率超过5%"
```

## 8. 总结

### 8.1 核心技术亮点

1. **分布式锁的精妙应用**: 使用packageUniqueId作为锁键，实现了细粒度的并发控制
2. **策略模式的业务分发**: 根据渠道类型选择不同的处理策略，提供了良好的扩展性
3. **双重检查的数据一致性**: 获取锁后重新查询状态，确保了数据的一致性
4. **完善的资源回收机制**: 涵盖数据库、缓存、外部系统的全方位资源清理

### 8.2 业务价值体现

1. **套餐生命周期管理**: 完整实现了套餐从激活到释放的生命周期管理
2. **多渠道类型支持**: 支持标准卡、终端线上、终端线下等多种渠道类型
3. **流量池特殊处理**: 针对流量池套餐的特殊业务规则进行了专门处理
4. **异常容错机制**: 完善的异常处理确保了系统的稳定性

### 8.3 架构设计优势

1. **高并发支持**: 通过分布式锁和异步处理支持高并发场景
2. **数据一致性**: 事务控制和双重检查确保了数据的一致性
3. **系统解耦**: 与外部系统的交互通过Feign客户端实现了松耦合
4. **可观测性**: 完善的日志记录和监控指标支持系统运维

### 8.4 改进建议

1. **性能优化**: 可考虑引入批量处理和缓存优化进一步提升性能
2. **监控增强**: 增加更细粒度的业务监控指标和告警规则
3. **测试覆盖**: 增加更多边界条件和异常场景的测试用例
4. **文档完善**: 补充更详细的运维手册和故障排查指南

通过本次深入分析，我们全面了解了handleActivatedOne方法在定向流量套餐释放中的核心作用和技术实现细节，为后续的系统维护、优化和扩展提供了重要的技术参考。
```
```
